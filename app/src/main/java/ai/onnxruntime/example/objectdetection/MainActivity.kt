package ai.onnxruntime.example.objectdetection

import android.graphics.BitmapFactory
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import ai.onnxruntime.OrtEnvironment

class MainActivity : AppCompatActivity() {
    private lateinit var ocrModel: OCRModel
    private lateinit var vocab: Vocab

    private lateinit var imageView: ImageView
    private lateinit var textView: TextView

    private var imageList = listOf("img_0.png", "img_1.png", "img_2.png")
    private var currentIndex = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        imageView = findViewById(R.id.imageView)
        textView = findViewById(R.id.resultText)
        val btnNext = findViewById<Button>(R.id.btnNext)
        val btnPrev = findViewById<Button>(R.id.btnPrev)

        // Load model và vocab
        val env = OrtEnvironment.getEnvironment()
        val modelBytes = assets.open("full_ocr.onnx").readBytes()
        ocrModel = OCRModel(env, modelBytes)
        vocab = Vocab.loadFromAssets(assets, "vocab.json")

        // Hiển thị ảnh đầu tiên
        showImageAndRunOCR()

        btnNext.setOnClickListener {
            currentIndex = (currentIndex + 1) % imageList.size
            showImageAndRunOCR()
        }

        btnPrev.setOnClickListener {
            currentIndex = if (currentIndex - 1 < 0) imageList.size - 1 else currentIndex - 1
            showImageAndRunOCR()
        }
    }

    private fun showImageAndRunOCR() {
        val imgName = imageList[currentIndex]
        val inputStream = assets.open(imgName)
        val bitmap = BitmapFactory.decodeStream(inputStream)

        imageView.setImageBitmap(bitmap)

        val tokens: IntArray = ocrModel.runOCR(bitmap)
        val text = vocab.decode(tokens.asIterable())

        textView.text = "Ảnh: $imgName\nKết quả OCR: $text"
    }
}
