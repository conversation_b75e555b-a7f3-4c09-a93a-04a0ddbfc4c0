package ai.onnxruntime.example.objectdetection

import android.content.res.AssetManager
import org.json.JSONObject

class Vocab(private val idx2char: Map<Int, String>) {
    fun decode(ids: Iterable<Int>): String {
            return ids.mapNotNull { idx2char[it] }.joinToString("")
    }

    companion object {
        fun loadFromAssets(assets: AssetManager, fileName: String): Vocab {
            val jsonStr = assets.open(fileName).bufferedReader().use { it.readText() }
            val jsonObj = JSONObject(jsonStr)

            val map = mutableMapOf<Int, String>()
            val keys = jsonObj.keys()
            while (keys.hasNext()) {
                val k = keys.next()
                map[k.toInt()] = jsonObj.getString(k)   // ✅ Sửa ở đây
            }
            return Vocab(map)
        }
    }
}
