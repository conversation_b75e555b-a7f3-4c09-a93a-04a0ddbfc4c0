package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.*
import android.graphics.Bitmap
import java.nio.FloatBuffer

class OCRModel(private val ortEnv: OrtEnvironment, modelBytes: ByteArray) {
    private val session: OrtSession

    init {
        val options = OrtSession.SessionOptions()
        session = ortEnv.createSession(modelBytes, options)
    }

    fun runOCR(bitmap: Bitmap): IntArray {
        // Resize ảnh đúng size mà model cần (ví dụ 128x32, tuỳ config VietOCR của bạn)
        val resized = Bitmap.createScaledBitmap(bitmap, 128, 32, true)
        val inputTensor = preprocessImage(resized)

        val inputName = session.inputNames.iterator().next()
        val result = session.run(mapOf(inputName to inputTensor))

        val output = result[0].value as Array<IntArray>
        return output[0]
    }

    private fun preprocessImage(bitmap: Bitmap): OnnxTensor {
        val w = bitmap.width
        val h = bitmap.height
        val floatBuffer = FloatBuffer.allocate(1 * h * w * 3)

        val pixels = IntArray(w * h)
        bitmap.getPixels(pixels, 0, w, 0, 0, w, h)

        for (y in 0 until h) {
            for (x in 0 until w) {
                val idx = y * w + x
                val pixel = pixels[idx]
                val r = (pixel shr 16 and 0xFF) / 255f
                val g = (pixel shr 8 and 0xFF) / 255f
                val b = (pixel and 0xFF) / 255f
                floatBuffer.put(r)
                floatBuffer.put(g)
                floatBuffer.put(b)
            }
        }
        floatBuffer.rewind()

        val shape = longArrayOf(1, h.toLong(), w.toLong(), 3) // [1, H, W, C]
        return OnnxTensor.createTensor(ortEnv, floatBuffer, shape)
    }
}
