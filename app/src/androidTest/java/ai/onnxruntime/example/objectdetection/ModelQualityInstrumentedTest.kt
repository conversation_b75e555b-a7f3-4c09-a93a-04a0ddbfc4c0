package ai.onnxruntime.example.objectdetection

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import kotlin.system.measureTimeMillis
import java.io.InputStream

/**
 * Instrumented test để test ONNX Runtime với native libraries
 * Chạy bằng: ./gradlew connectedAndroidTest
 */
@RunWith(AndroidJUnit4::class)
class ModelQualityInstrumentedTest {
    
    companion object {
        private const val CONFIDENCE_THRESHOLD = 0.5f
        private const val MIN_DETECTION_COUNT = 1
        private const val MAX_INFERENCE_TIME_MS = 5000L // 5 giây
    }

    @Test
    fun testBasicSetup() {
        println("=== TEST CƠ BẢN INSTRUMENTED ===")

        val context = InstrumentationRegistry.getInstrumentation().targetContext
        assertNotNull("Context không được null", context)

        // Kiểm tra assets có tồn tại không
        try {
            val modelStream: InputStream = context.assets.open("best.onnx")
            val modelSize = modelStream.available()
            modelStream.close()
            assertTrue("Model file phải có kích thước > 0", modelSize > 0)
            println("✓ Model file tồn tại: $modelSize bytes")
        } catch (e: Exception) {
            println("⚠ Model file không tồn tại: ${e.message}")
        }

        try {
            val imgStream: InputStream = context.assets.open("img.png")
            val imgSize = imgStream.available()
            imgStream.close()
            assertTrue("Image file phải có kích thước > 0", imgSize > 0)
            println("✓ Image file tồn tại: $imgSize bytes")
        } catch (e: Exception) {
            println("⚠ Image file không tồn tại: ${e.message}")
        }

        println("✓ Basic setup test hoàn thành")
    }

    @Test
    fun testOnnxRuntimeInit() {
        println("=== TEST ONNX RUNTIME INIT ===")

        try {
            val env = OrtEnvironment.getEnvironment()
            assertNotNull("ONNX Environment không được null", env)
            println("✓ ONNX Runtime khởi tạo thành công")

            env.close()
            println("✓ ONNX Runtime cleanup thành công")
        } catch (e: Exception) {
            println("❌ Lỗi ONNX Runtime: ${e.message}")
            fail("ONNX Runtime không hoạt động: ${e.message}")
        }
    }

    private fun evaluateModelQuality(result: Result, inferenceTime: Long) {
        println("\n=== ĐÁNH GIÁ CHẤT LƯỢNG MODEL ===")
        
        // Test 1: Kiểm tra có phát hiện được object không
        assertTrue("❌ Model không phát hiện được object nào", result.boxes.isNotEmpty())
        println("✓ Model phát hiện được ${result.boxes.size} objects")
        
        // Test 2: Kiểm tra thời gian inference
        assertTrue("❌ Thời gian inference quá chậm: ${inferenceTime}ms > ${MAX_INFERENCE_TIME_MS}ms", 
                  inferenceTime <= MAX_INFERENCE_TIME_MS)
        println("✓ Thời gian inference hợp lý: ${inferenceTime}ms")
        
        // Test 3: Kiểm tra confidence score
        val highConfidenceBoxes = result.boxes.filter { it[4] >= CONFIDENCE_THRESHOLD }
        println("✓ Có ${highConfidenceBoxes.size}/${result.boxes.size} objects với confidence >= $CONFIDENCE_THRESHOLD")
        
        // Test 4: Kiểm tra tính hợp lệ của bounding boxes
        var validBoxes = 0
        result.boxes.forEach { box ->
            val x1 = box[0]
            val y1 = box[1] 
            val x2 = box[2]
            val y2 = box[3]
            
            if (x1 < x2 && y1 < y2 && x1 >= 0 && y1 >= 0) {
                validBoxes++
            }
        }
        
        assertTrue("❌ Có bounding boxes không hợp lệ", validBoxes == result.boxes.size)
        println("✓ Tất cả ${validBoxes} bounding boxes đều hợp lệ")
        
        // Tính toán metrics
        calculateMetrics(result)
    }
    
    private fun calculateMetrics(result: Result) {
        println("\n=== METRICS CHI TIẾT ===")
        
        if (result.boxes.isEmpty()) {
            println("Không có objects để tính metrics")
            return
        }
        
        // Confidence statistics
        val confidences = result.boxes.map { it[4] }
        val avgConfidence = confidences.average()
        val maxConfidence = confidences.maxOrNull() ?: 0f
        val minConfidence = confidences.minOrNull() ?: 0f
        
        println("Confidence trung bình: ${String.format("%.3f", avgConfidence)}")
        println("Confidence cao nhất: ${String.format("%.3f", maxConfidence)}")
        println("Confidence thấp nhất: ${String.format("%.3f", minConfidence)}")
        
        // Class distribution
        val classIds = result.boxes.map { it[5].toInt() }
        val classDistribution = classIds.groupingBy { it }.eachCount()
        println("Phân bố classes:")
        classDistribution.forEach { (classId, count) ->
            println("  Class $classId: $count objects")
        }
        
        // Box size analysis
        val boxSizes = result.boxes.map { box ->
            val width = box[2] - box[0]
            val height = box[3] - box[1]
            width * height
        }
        val avgBoxSize = boxSizes.average()
        println("Kích thước box trung bình: ${String.format("%.2f", avgBoxSize)}")
        
        println("\n=== HOÀN THÀNH TEST ===")
    }
}
