package ai.onnxruntime.example.objectdetection

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith

/**
 * Simple instrumented test để kiểm tra thiết bị Android
 */
@RunWith(AndroidJUnit4::class)
class SimpleInstrumentedTest {

    @Test
    fun testDeviceConnection() {
        println("=== TEST KẾT NỐI THIẾT BỊ ===")
        
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertNotNull("App context không được null", appContext)
        
        val packageName = appContext.packageName
        assertEquals("ai.onnxruntime.example.objectdetection", packageName)
        
        println("✓ Package name: $packageName")
        println("✓ Thiết bị Android kết nối thành công")
    }

    @Test
    fun testAssetsAccess() {
        println("=== TEST TRUY CẬP ASSETS ===")
        
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        val assetManager = context.assets
        
        try {
            val assetList = assetManager.list("")
            assertNotNull("Asset list không được null", assetList)
            assertTrue("Phải có ít nhất 1 asset", assetList!!.isNotEmpty())
            
            println("✓ Assets có sẵn:")
            assetList.forEach { asset ->
                println("  - $asset")
            }
            
        } catch (e: Exception) {
            println("❌ Lỗi truy cập assets: ${e.message}")
            fail("Không thể truy cập assets: ${e.message}")
        }
    }
}
