package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import ai.onnxruntime.OnnxTensor
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.junit.After
import java.io.File
import java.nio.FloatBuffer
import kotlin.system.measureTimeMillis

/**
 * Desktop test cho ONNX model - không cần Android
 * Chạy trực tiếp trên JVM với ONNX Runtime Java
 */
class DesktopModelTest {

    private lateinit var ortEnv: OrtEnvironment
    private lateinit var ortSession: OrtSession

    companion object {
        private const val MODEL_PATH = "app/src/test/resources/best.onnx"
        private const val INPUT_SIZE = 640
        private const val CONFIDENCE_THRESHOLD = 0.25f
        private const val MAX_INFERENCE_TIME_MS = 5000L
    }

    @Before
    fun setup() {
        println("=== KHỞI TẠO DESKTOP MODEL TEST ===")

        // Khởi tạo ONNX Runtime environment
        ortEnv = OrtEnvironment.getEnvironment()

        // Load model từ file
        val modelFile = File(MODEL_PATH)
        assertTrue("Model file phải tồn tại: $MODEL_PATH", modelFile.exists())

        ortSession = ortEnv.createSession(modelFile.absolutePath)
        println("✓ Đã load model thành công: ${modelFile.length()} bytes")
    }

    @After
    fun cleanup() {
        ortSession.close()
        ortEnv.close()
        println("✓ Đã cleanup resources")
    }

    @Test
    fun testModelInfo() {
        println("=== TEST THÔNG TIN MODEL ===")

        // Kiểm tra input/output của model
        val inputInfo = ortSession.inputInfo
        val outputInfo = ortSession.outputInfo

        assertTrue("Model phải có ít nhất 1 input", inputInfo.isNotEmpty())
        assertTrue("Model phải có ít nhất 1 output", outputInfo.isNotEmpty())

        // In thông tin chi tiết
        println("Inputs:")
        inputInfo.forEach { (name, info) ->
            println("  - $name: ${info.info}")
        }

        println("Outputs:")
        outputInfo.forEach { (name, info) ->
            println("  - $name: ${info.info}")
        }

        println("✓ Model info hợp lệ")
    }

    @Test
    fun testModelInference() {
        println("=== TEST INFERENCE MODEL ===")

        // Tạo input tensor giả (640x640x3)
        val inputData = createMockImageData()
        val inputShape = longArrayOf(1, 3, INPUT_SIZE.toLong(), INPUT_SIZE.toLong())

        val inputTensor = OnnxTensor.createTensor(ortEnv, FloatBuffer.wrap(inputData), inputShape)

        var inferenceTime: Long = 0
        var output: OrtSession.Result? = null

        inputTensor.use {
            inferenceTime = measureTimeMillis {
                output = ortSession.run(mapOf("images" to inputTensor))
            }
        }

        output?.use { result ->
            println("✓ Inference thành công trong ${inferenceTime}ms")

            // Kiểm tra thời gian inference
            assertTrue("Inference time phải < ${MAX_INFERENCE_TIME_MS}ms",
                      inferenceTime < MAX_INFERENCE_TIME_MS)

            // Kiểm tra output
            assertTrue("Phải có output", result.size() > 0)

            val outputTensor = result[0]
            assertNotNull("Output tensor không được null", outputTensor)

            println("✓ Output shape: ${outputTensor?.info}")
            println("✓ Inference time: ${inferenceTime}ms")
            println("✓ FPS: ${String.format("%.2f", 1000f / inferenceTime)}")
        }
    }

    @Test
    fun testModelPerformance() {
        println("=== TEST HIỆU SUẤT MODEL ===")

        val inputData = createMockImageData()
        val inputShape = longArrayOf(1, 3, INPUT_SIZE.toLong(), INPUT_SIZE.toLong())

        val times = mutableListOf<Long>()
        val numRuns = 5

        // Chạy nhiều lần để đo hiệu suất
        repeat(numRuns) { i ->
            val inputTensor = OnnxTensor.createTensor(ortEnv, FloatBuffer.wrap(inputData), inputShape)

            inputTensor.use {
                val time = measureTimeMillis {
                    ortSession.run(mapOf("images" to inputTensor)).use { }
                }
                times.add(time)
                println("Run ${i + 1}: ${time}ms")
            }
        }

        // Tính toán statistics
        val avgTime = times.average()
        val minTime = times.minOrNull() ?: 0L
        val maxTime = times.maxOrNull() ?: 0L
        val avgFps = 1000f / avgTime

        println("\n=== KẾT QUẢ HIỆU SUẤT ===")
        println("Số lần chạy: $numRuns")
        println("Thời gian trung bình: ${String.format("%.2f", avgTime)}ms")
        println("Thời gian nhanh nhất: ${minTime}ms")
        println("Thời gian chậm nhất: ${maxTime}ms")
        println("FPS trung bình: ${String.format("%.2f", avgFps)}")

        // Assertions
        assertTrue("Thời gian trung bình phải < ${MAX_INFERENCE_TIME_MS}ms", avgTime < MAX_INFERENCE_TIME_MS)
        assertTrue("FPS phải > 0", avgFps > 0f)

        println("✓ Hiệu suất model đạt yêu cầu")
    }

    @Test
    fun testModelStability() {
        println("=== TEST ỔN ĐỊNH MODEL ===")

        val inputData = createMockImageData()
        val inputShape = longArrayOf(1, 3, INPUT_SIZE.toLong(), INPUT_SIZE.toLong())

        // Test chạy liên tục nhiều lần
        repeat(10) { i ->
            val inputTensor = OnnxTensor.createTensor(ortEnv, FloatBuffer.wrap(inputData), inputShape)

            inputTensor.use {
                val result = ortSession.run(mapOf("images" to inputTensor))
                result.use {
                    assertNotNull("Output ${i + 1} không được null", result[0])
                }
            }
        }

        println("✓ Model ổn định qua 10 lần chạy")
    }

    /**
     * Tạo dữ liệu ảnh giả để test
     * Trong thực tế có thể load ảnh thật từ file
     */
    private fun createMockImageData(): FloatArray {
        val size = 3 * INPUT_SIZE * INPUT_SIZE
        val data = FloatArray(size)

        // Tạo dữ liệu random normalized [0, 1]
        for (i in data.indices) {
            data[i] = Math.random().toFloat()
        }

        return data
    }
}
